import { type Profile } from './auth';

export type UserBasicInfo = {
  id: string;
  full_name: string;
  avatar_url?: string;
};

export type Match = {
  id: string;
  created_at: string;
  family: Profile;
  aupair: Profile;
};

export type SimpleMatch = {
  id: string;
  created_at: string;
  family: UserBasicInfo;
  aupair: UserBasicInfo;
};

export type MatchWithRelations = {
  id: string;
  created_at: string;
  families: { id: string; family_name: string };
  au_pairs: { id: string; first_name: string; last_name: string };
};
