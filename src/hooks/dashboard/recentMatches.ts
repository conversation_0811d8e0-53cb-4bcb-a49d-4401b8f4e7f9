import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/api/supabase';
import type { SimpleMatch, MatchWithRelations } from '@/types/types';

export function useRecentMatches() {
  return useQuery<SimpleMatch[]>({
    queryKey: ['dashboard', 'recent-matches'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('matches')
        .select(`id,created_at, families:family_id(id, family_name) ,au_pairs:au_pair_id(id, first_name, last_name)`)
        .eq('status', 'confirmed')
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;
      if (!data || data.length === 0) return [];

      return (data as unknown as MatchWithRelations[]).map(match => ({
        id: match.id,
        created_at: match.created_at,
        family: {
          id: match.families?.id,
          full_name: match.families?.family_name,
          avatar_url: undefined,
        },
        aupair: {
          id: match.au_pairs?.id,
          full_name: match.au_pairs ? `${match.au_pairs.first_name} ${match.au_pairs.last_name}`.trim() : '',
          avatar_url: undefined,
        },
      }));
    },
    refetchIntervalInBackground: true,
    refetchInterval: 60000, // gets new data in the background every minute
  });
}
