import { createContext, useState, useContext } from 'react';
import type { ReactNode } from 'react';

type MobileMenuId = 'main' | 'familyJourney' | null;

interface MobileMenuControlContextType {
  activeMobileMenu: MobileMenuId;
  openMobileMenu: (menuId: NonNullable<MobileMenuId>) => void;
  closeMobileMenu: () => void;
  toggleMobileMenu: (menuId: NonNullable<MobileMenuId>) => void;
}

const MobileMenuControlContext = createContext<MobileMenuControlContextType | undefined>(undefined);

export const MobileMenuControlProvider = ({ children }: { children: ReactNode }) => {
  const [activeMobileMenu, setActiveMobileMenu] = useState<MobileMenuId>(null);

  const openMobileMenu = (menuId: NonNullable<MobileMenuId>) => {
    setActiveMobileMenu(menuId);
  };

  const closeMobileMenu = () => {
    setActiveMobileMenu(null);
  };

  const toggleMobileMenu = (menuId: NonNullable<MobileMenuId>) => {
    setActiveMobileMenu(prev => (prev === menuId ? null : menuId));
  };

  return (
    <MobileMenuControlContext.Provider value={{ activeMobileMenu, openMobileMenu, closeMobileMenu, toggleMobileMenu }}>
      {children}
    </MobileMenuControlContext.Provider>
  );
};

export const useMobileMenuControl = () => {
  const context = useContext(MobileMenuControlContext);
  if (context === undefined) {
    throw new Error('useMobileMenuControl must be used within a MobileMenuControlProvider');
  }
  return context;
};
