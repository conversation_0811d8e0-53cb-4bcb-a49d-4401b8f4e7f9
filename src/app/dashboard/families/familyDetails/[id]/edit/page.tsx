import { But<PERSON> } from '@/components/ui/button';
import { Link, useParams } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { DashboardShell } from '@/components/ui/dashboard-shell';
import { DashboardHeader } from '@/components/ui/dashboard-header';
import { FamilyDetails } from '@/components/dashboard/families/famlyDetails';

export default function EditFamilyPage() {
  const { id } = useParams<{ id: string }>();
  return (
    <DashboardShell>
      <DashboardHeader heading="Edit Family" text={`Update information for family ID: ${id}`}>
        <Link to={`/dashboard/families/${id}/edit`}>
          <Button variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
          </Button>
        </Link>
      </DashboardHeader>
      <FamilyDetails id={id!} />
    </DashboardShell>
  );
}
