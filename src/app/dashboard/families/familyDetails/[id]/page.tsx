import { But<PERSON> } from '@/components/ui/button';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, Edit, Download, Mail } from 'lucide-react';
import { FamilyDetails } from '@/components/dashboard/families/famlyDetails';

interface FamilyDetails {
  params: {
    id: string;
  };
}

export default function FamilyDetailsPage({ params }: FamilyDetails) {
  return (
    <div className="flex flex-col gap-4 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Link to="/dashboard/families">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-3xl font-bold tracking-tight">Family Details</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Mail className="mr-2 h-4 w-4" />
            Contact
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Link to={`/dashboard/families/${params.id}/edit`}>
            <Button size="sm">
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          </Link>
        </div>
      </div>
      <FamilyDetails id={params.id} />
    </div>
  );
}
