import { type ReactNode, useEffect } from 'react';
import { Navigate, useNavigate } from 'react-router';
import { useAuth } from '@/hooks/auth';

interface AuthRedirectProps {
  children: ReactNode;
  redirectTo?: string;
}

export function AuthRedirect({ children, redirectTo = '/dashboard' }: AuthRedirectProps) {
  const { isAuthenticated, isUserLoading, isProfileLoading, error } = useAuth();
  const navigate = useNavigate();

  const isLoading = isUserLoading || isProfileLoading;

  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      navigate(redirectTo, { replace: true });
    }
  }, [isAuthenticated, isLoading, navigate, redirectTo]);

  if (isLoading && !error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (isAuthenticated) {
    return <Navigate to={redirectTo} replace />;
  }

  return <>{children}</>;
}
