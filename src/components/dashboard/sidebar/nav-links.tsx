// This file should be moved to src/components/dashboard/dashboard-nav-links.tsx
import { Link, useLocation } from 'react-router';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  BarChartIcon,
  ActivityIcon,
  BadgeCheck,
  SettingsIcon,
  HelpCircleIcon,
  UsersIcon,
  HomeIcon,
} from 'lucide-react';

interface NavItemProps {
  to: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  isActive?: boolean;
}

interface DashboardNavLinksProps {
  className?: string;
  collapsed?: boolean;
}

export function DashboardNavLinks({ className, collapsed = false }: DashboardNavLinksProps) {
  const location = useLocation();
  const pathname = location.pathname;
  const isActive = (path: string) => {
    if (path === '/dashboard/overview' && pathname === '/dashboard') {
      return true;
    }
    if (path !== '/dashboard' && pathname.startsWith(path)) {
      return true;
    }
    return false;
  };

  return (
    <nav>
      <div className={cn('dashboard-nav-links', className)}>
        <div className="space-y-1">
          <NavItem
            to="/dashboard"
            icon={<BarChartIcon className="h-4 w-4" />}
            isActive={isActive('/dashboard/overview')}
            collapsed={collapsed}
          >
            Overview
          </NavItem>
          <NavItem
            to="/dashboard/activity"
            icon={<ActivityIcon className="h-4 w-4" />}
            isActive={isActive('/dashboard/activity')}
            collapsed={collapsed}
          >
            Activity
          </NavItem>
          <NavItem
            to="/dashboard/au-pairs"
            icon={<UsersIcon className="h-4 w-4" />}
            isActive={isActive('/dashboard/au-pairs')}
            collapsed={collapsed}
          >
            Au Pairs
          </NavItem>
          <NavItem
            to="/dashboard/families"
            icon={<HomeIcon className="h-4 w-4" />}
            isActive={isActive('/dashboard/families')}
            collapsed={collapsed}
          >
            Families
          </NavItem>
          <NavItem
            to="/dashboard/matching"
            icon={<BadgeCheck className="h-4 w-4" />}
            isActive={isActive('/dashboard/matching')}
            collapsed={collapsed}
          >
            Matching
          </NavItem>
        </div>

        <div className={cn('space-y-1', !collapsed && 'mt-6')}>
          {!collapsed && (
            <h3 className="px-4 text-xs font-semibold text-muted-foreground">System</h3>
          )}
          <NavItem
            to="/dashboard/settings"
            icon={<SettingsIcon className="h-4 w-4" />}
            isActive={isActive('/dashboard/settings')}
            collapsed={collapsed}
          >
            Settings
          </NavItem>
          <NavItem
            to="/dashboard/help"
            icon={<HelpCircleIcon className="h-4 w-4" />}
            isActive={isActive('/dashboard/help')}
            collapsed={collapsed}
          >
            Help & Support
          </NavItem>
        </div>
      </div>
    </nav>
  );
}

function NavItem({ to, icon, children, isActive, collapsed = false }: NavItemProps & { collapsed?: boolean }) {
  return (
    <Link to={to} className="block">
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          'w-full justify-start gap-2',
          isActive ? 'bg-accent text-accent-foreground' : 'hover:bg-accent/50'
        )}
      >
        {icon}
        {!collapsed && <span>{children}</span>}
      </Button>
    </Link>
  );
}
