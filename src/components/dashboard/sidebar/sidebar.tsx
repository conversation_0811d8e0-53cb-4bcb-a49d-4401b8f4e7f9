import { cn } from '@/lib/utils';
import { DashboardNavLinks } from './nav-links';
import { useSidebarContext } from './sideBarContext';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

interface DashboardSidebarProps {
  isMobileMenuOpen: boolean;
  closeMobileMenu: () => void;
  className?: string;
}

export function DashboardSidebar({ isMobileMenuOpen, closeMobileMenu, className }: DashboardSidebarProps) {
  const { sidebarCollapsed, toggleSidebar } = useSidebarContext();

  return (
    <>
      {/* Mobile backdrop */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-10 bg-black/40 backdrop-blur-sm md:hidden"
          onClick={closeMobileMenu}
          aria-hidden="true"
        />
      )}
      
      <aside
        className={cn(
          'fixed inset-y-0 left-0 z-50 shrink-0 border-r border-border/40 bg-background/95 backdrop-blur-sm shadow-md transition-all duration-300 ease-in-out',
          'md:static md:z-0 md:mt-0 md:shadow-none md:translate-x-0 md:h-full flex flex-col',
          isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0',
          sidebarCollapsed ? 'w-16' : 'w-64',
          className
        )}
        style={{ minWidth: sidebarCollapsed ? 64 : 256 }}
      >
        {/* Mobile close button */}
        {isMobileMenuOpen && (
          <div className="flex justify-end p-2 md:hidden">
            <Button 
              variant="ghost" 
              size="icon" 
              className="h-8 w-8" 
              onClick={closeMobileMenu}
              aria-label="Close sidebar"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
        
        {/* Collapse/Expand Button (desktop only) */}
        <button
          className={cn(
            'absolute top-4 -right-3 z-30 bg-background border border-border/60 rounded-full p-1.5 shadow-md transition-all duration-200 hover:shadow-lg',
            'hidden md:flex md:items-center md:justify-center transform hover:scale-105 active:scale-95'
          )}
          style={{ right: '-16px' }}
          onClick={toggleSidebar}
          aria-label={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          {sidebarCollapsed ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
        </button>

        <div className={cn(
          "flex h-full flex-col gap-2 p-4",
          sidebarCollapsed && "p-2"
        )}>
          <div className={cn("py-2", sidebarCollapsed && "py-1")}>
            <DashboardNavLinks collapsed={sidebarCollapsed} />
          </div>
        </div>
      </aside>
    </>
  );
}
