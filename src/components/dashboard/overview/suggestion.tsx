'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, Check, MapPin, Users } from 'lucide-react';
import { Link } from 'react-router-dom';

interface MatchData {
  id: string;
  aupair: {
    id: string;
    name: string;
    country: string;
    age: number;
    avatar: string;
    initials: string;
  };
  family: {
    id: string;
    name: string;
    location: string;
    children: number;
    avatar: string;
    initials: string;
  };
  compatibility: number;
  matchReasons: string[];
}

// Mock data for suggestions
const matchesData: MatchData[] = [
  {
    id: 'match1',
    aupair: {
      id: 'au1',
      name: '<PERSON>',
      country: 'United States',
      age: 24,
      avatar: '/placeholder.svg?height=40&width=40',
      initials: 'E<PERSON>',
    },
    family: {
      id: 'fam1',
      name: '<PERSON>',
      location: 'New York, USA',
      children: 2,
      avatar: '/placeholder.svg?height=40&width=40',
      initials: 'SF',
    },
    compatibility: 94,
    matchReasons: ['Similar interests in outdoor activities', 'Language match'],
  },
  {
    id: 'match2',
    aupair: {
      id: 'au2',
      name: '<PERSON>',
      country: 'Germany',
      age: 22,
      avatar: '/placeholder.svg?height=40&width=40',
      initials: 'LM',
    },
    family: {
      id: 'fam2',
      name: '<PERSON> Family',
      location: 'Berlin, Germany',
      children: 3,
      avatar: '/placeholder.svg?height=40&width=40',
      initials: 'GF',
    },
    compatibility: 88,
    matchReasons: ['German language native', 'Experience with multiple children'],
  },
  {
    id: 'match3',
    aupair: {
      id: 'au3',
      name: 'Sophie Martin',
      country: 'France',
      age: 23,
      avatar: '/placeholder.svg?height=40&width=40',
      initials: 'SM',
    },
    family: {
      id: 'fam3',
      name: 'Kim Family',
      location: 'Paris, France',
      children: 1,
      avatar: '/placeholder.svg?height=40&width=40',
      initials: 'KF',
    },
    compatibility: 92,
    matchReasons: ['Experience with special needs', 'French native speaker'],
  },
  {
    id: 'match4',
    aupair: {
      id: 'au4',
      name: 'Ahmed Hassan',
      country: 'Egypt',
      age: 25,
      avatar: '/placeholder.svg?height=40&width=40',
      initials: 'AH',
    },
    family: {
      id: 'fam4',
      name: 'Patel Family',
      location: 'London, UK',
      children: 2,
      avatar: '/placeholder.svg?height=40&width=40',
      initials: 'PF',
    },
    compatibility: 90,
    matchReasons: ['Experience with school-aged children', 'Arabic language skills'],
  },
];

export function Suggestions() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Suggested Matches</h3>
          <p className="text-sm text-muted-foreground">Based on preferences and compatibility</p>
        </div>
        <Link to="/dashboard/matches">
          <Button variant="outline" size="sm">
            View All Matches
          </Button>
        </Link>
      </div>
      <div className="grid gap-4">
        {matchesData.map(match => (
          <MatchCard key={match.id} match={match} />
        ))}
      </div>
    </div>
  );
}

function MatchCard({ match }: { match: MatchData }) {
  // Determine color based on compatibility percentage
  const getCompatibilityColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-green-500';
    if (percentage >= 80) return 'bg-emerald-500';
    if (percentage >= 70) return 'bg-blue-500';
    if (percentage >= 60) return 'bg-yellow-500';
    return 'bg-orange-500';
  };

  return (
    <Card className="overflow-hidden">
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Top row: Match info */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-12">
            {/* Au Pair */}
            <div className="md:col-span-4">
              <div className="flex items-center space-x-3">
                <Avatar>
                  <AvatarImage src={match.aupair.avatar || '/placeholder.svg'} alt={match.aupair.name} />
                  <AvatarFallback>{match.aupair.initials}</AvatarFallback>
                </Avatar>
                <div className="min-w-0">
                  <p className="truncate text-sm font-medium">{match.aupair.name}</p>
                  <p className="truncate text-xs text-muted-foreground">
                    {match.aupair.country}, {match.aupair.age} years
                  </p>
                </div>
              </div>
            </div>

            {/* Arrow */}
            <div className="flex items-center justify-center md:col-span-1">
              <ArrowRight className="h-4 w-4 text-muted-foreground" />
            </div>

            {/* Family */}
            <div className="md:col-span-4">
              <div className="flex items-center space-x-3">
                <Avatar>
                  <AvatarImage src={match.family.avatar || '/placeholder.svg'} alt={match.family.name} />
                  <AvatarFallback>{match.family.initials}</AvatarFallback>
                </Avatar>
                <div className="min-w-0">
                  <p className="truncate text-sm font-medium">{match.family.name}</p>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <MapPin className="mr-1 h-3 w-3 flex-shrink-0" />
                    <span className="truncate">{match.family.location}</span>
                    <Users className="ml-2 mr-1 h-3 w-3 flex-shrink-0" />
                    <span>
                      {match.family.children} {match.family.children === 1 ? 'child' : 'children'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Compatibility Score */}
            <div className="md:col-span-3">
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Compatibility</span>
                  <span className="text-sm font-bold">{match.compatibility}%</span>
                </div>
                <div className="h-2 w-full rounded-full bg-gray-100 dark:bg-gray-800">
                  <div
                    className={`h-2 rounded-full ${getCompatibilityColor(match.compatibility)}`}
                    style={{ width: `${match.compatibility}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom row: Compatibility reasons */}
          <div className="pt-2">
            <div className="flex flex-wrap gap-2">
              {match.matchReasons.map((reason, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="max-w-full flex items-center text-xs truncate"
                  title={reason}
                >
                  <Check className="mr-1 h-3 w-3 flex-shrink-0" />
                  <span className="truncate">{reason}</span>
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
