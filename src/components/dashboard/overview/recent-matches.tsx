import { Card, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

// Mock data for recent matches
const recentMatches = [
  {
    id: 'recent1',
    aupair: {
      full_name: '<PERSON>',
    },
    family: {
      full_name: '<PERSON>',
    },
    created_at: new Date(Date.now() - 0 * 86400000).toISOString(),
  },
  {
    id: 'recent2',
    aupair: {
      full_name: '<PERSON>',
    },
    family: {
      full_name: '<PERSON>',
    },
    created_at: new Date(Date.now() - 1 * 86400000).toISOString(),
  },
  {
    id: 'recent3',
    aupair: {
      full_name: '<PERSON>',
    },
    family: {
      full_name: '<PERSON>',
    },
    created_at: new Date(Date.now() - 2 * 86400000).toISOString(),
  },
  {
    id: 'recent4',
    aupair: {
      full_name: '<PERSON>',
    },
    family: {
      full_name: '<PERSON>',
    },
    created_at: new Date(Date.now() - 3 * 86400000).toISOString(),
  },
];

const isLoading = false;

export function RecentMatches() {
  return (
    <Card className="shadow-sm relative">
      <CardHeader>
        <CardTitle>Recent Matches</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10 rounded-lg">
            <span className="h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></span>
          </div>
        )}
        {!recentMatches?.length ? (
          <div className="text-center py-8 text-muted-foreground">No recent matches found</div>
        ) : (
          <div className="space-y-4">
            {recentMatches.map(match => (
              <div key={match.id} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback>{match.aupair.full_name.substring(0, 2)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{match.aupair.full_name}</div>
                    <div className="text-sm text-muted-foreground">Matched with {match.family.full_name}</div>
                  </div>
                </div>
                <Badge variant="outline">{new Date(match.created_at).toLocaleDateString()}</Badge>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
