import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { HomeIcon, UsersIcon, HeartIcon, CheckCircleIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

// Mock data for overview stats
const mockOverviewData = {
  families: 24,
  auPairs: 18,
  matches: 15,
  suggested: 8,
};

const stats = {
  families: mockOverviewData.families,
  auPairs: mockOverviewData.auPairs,
  matches: mockOverviewData.matches,
  suggested: mockOverviewData.suggested,
};
const isLoading = false;

export function OverviewPage() {
  const dashboardItems = [
    {
      title: 'Total Families',
      value: isLoading ? '...' : stats.families.toString(),
      icon: HomeIcon,
      color: 'text-black',
    },
    {
      title: 'Total Au Pairs',
      value: isLoading ? '...' : stats.auPairs.toString(),
      icon: UsersIcon,
      color: 'text-black',
    },
    {
      title: 'Successful Matches',
      value: isLoading ? '...' : stats.matches.toString(),
      icon: HeartIcon,
      color: 'text-black',
    },
    {
      title: 'Suggested Matches',
      value: isLoading ? '...' : stats.suggested.toString(),
      icon: CheckCircleIcon,
      color: 'text-black',
    },
  ];

  return (
    <div>
      <div className="grid gap-2 md:grid-cols-4">
        {dashboardItems.map((item, index) => (
          <Card key={index} className="overflow-auto shadow-sm">
            <CardHeader className="flex flex-auto justify-center pb-2">
              <CardTitle className="text-sm font-medium">{item.title}</CardTitle>
              <item.icon className={cn('h-5 w-5', item.color)} />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{item.value}</div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
