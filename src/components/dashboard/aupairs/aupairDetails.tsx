import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Calendar, MapPin, Phone, Mail, GraduationCap, Car, Heart, Users } from 'lucide-react';

// Mock data for a single au pair
const aupairData = {
  id: '1',
  firstName: 'Maria',
  lastName: '<PERSON>',
  email: '<EMAIL>',
  phone: '+49 123 456789',
  gender: 'Female',
  dateOfBirth: '1998-05-15',
  streetAddress: 'Hauptstraße',
  streetNumber: '42',
  city: 'Berlin',
  postalCode: '10115',
  country: 'Germany',
  earliestStartDate: '2023-09-01',
  latestStartDate: '2023-10-15',
  highestEducation: "Bachelor's Degree",
  program: 'Cultural Exchange',
  experienceWithKids: '3 years as a babysitter and camp counselor',
  sportsAndInterests: 'Swimming, piano, reading, hiking',
  maxChildren: 3,
  acceptsPets: true,
  hasDriversLicense: true,
};

export function AupairDetails({ id }: { id: string }) {
  return (
    <Tabs defaultValue="personal" className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="personal">Personal Information</TabsTrigger>
        <TabsTrigger value="preferences">Preferences</TabsTrigger>
        <TabsTrigger value="qualifications">Qualifications</TabsTrigger>
      </TabsList>
      <TabsContent value="personal" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Personal Details</CardTitle>
            <CardDescription>Basic information about the au pair.</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium">Full Name</p>
                <p className="text-sm text-muted-foreground flex items-center">
                  {aupairData.firstName} {aupairData.lastName}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Gender</p>
                <p className="text-sm text-muted-foreground">{aupairData.gender}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Date of Birth</p>
                <p className="text-sm text-muted-foreground flex items-center">
                  <Calendar className="mr-2 h-4 w-4" />
                  {new Date(aupairData.dateOfBirth).toLocaleDateString()}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Email</p>
                <p className="text-sm text-muted-foreground flex items-center">
                  <Mail className="mr-2 h-4 w-4" />
                  {aupairData.email}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Phone</p>
                <p className="text-sm text-muted-foreground flex items-center">
                  <Phone className="mr-2 h-4 w-4" />
                  {aupairData.phone}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Address</CardTitle>
            <CardDescription>Current residence information.</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium">Street Address</p>
                <p className="text-sm text-muted-foreground">
                  {aupairData.streetAddress} {aupairData.streetNumber}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">City</p>
                <p className="text-sm text-muted-foreground">{aupairData.city}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Postal Code</p>
                <p className="text-sm text-muted-foreground">{aupairData.postalCode}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Country</p>
                <p className="text-sm text-muted-foreground flex items-center">
                  <MapPin className="mr-2 h-4 w-4" />
                  {aupairData.country}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="preferences" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Availability</CardTitle>
            <CardDescription>When the au pair is available to start.</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium">Earliest Start Date</p>
                <p className="text-sm text-muted-foreground">
                  {new Date(aupairData.earliestStartDate).toLocaleDateString()}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Latest Start Date</p>
                <p className="text-sm text-muted-foreground">
                  {new Date(aupairData.latestStartDate).toLocaleDateString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Preferences</CardTitle>
            <CardDescription>Au pair's preferences for placement.</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium">Maximum Number of Children</p>
                <p className="text-sm text-muted-foreground flex items-center">
                  <Users className="mr-2 h-4 w-4" />
                  {aupairData.maxChildren}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Accepts Families with Pets</p>
                <p className="text-sm text-muted-foreground">
                  {aupairData.acceptsPets ? (
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Yes</Badge>
                  ) : (
                    <Badge className="bg-red-100 text-red-800 hover:bg-red-100">No</Badge>
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
      <TabsContent value="qualifications" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Education & Experience</CardTitle>
            <CardDescription>Au pair's educational background and experience with children.</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium">Highest Education</p>
                <p className="text-sm text-muted-foreground flex items-center">
                  <GraduationCap className="mr-2 h-4 w-4" />
                  {aupairData.highestEducation}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Program</p>
                <p className="text-sm text-muted-foreground">{aupairData.program}</p>
              </div>
              <div className="space-y-1 col-span-2">
                <p className="text-sm font-medium">Experience with Kids</p>
                <p className="text-sm text-muted-foreground">{aupairData.experienceWithKids}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Skills & Interests</CardTitle>
            <CardDescription>Au pair's skills, interests, and qualifications.</CardDescription>
          </CardHeader>
          <CardContent className="grid gap-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm font-medium">Driver's License</p>
                <p className="text-sm text-muted-foreground flex items-center">
                  <Car className="mr-2 h-4 w-4" />
                  {aupairData.hasDriversLicense ? 'Yes' : 'No'}
                </p>
              </div>
              <div className="space-y-1 col-span-2">
                <p className="text-sm font-medium">Sports & Interests</p>
                <p className="text-sm text-muted-foreground flex items-center">
                  <Heart className="mr-2 h-4 w-4" />
                  {aupairData.sportsAndInterests}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
}
