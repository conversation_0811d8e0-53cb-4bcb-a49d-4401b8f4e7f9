import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { MapPin, Phone, Mail, Home, Briefcase, Calendar, PawPrint, Utensils, Heart, FileText } from 'lucide-react';
import { Link } from 'react-router-dom';

// Mock data for a single family
const familyData = {
  id: '1',
  mainContact: {
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phone: '+49 123 456789',
    email: '<EMAIL>',
  },
  address: {
    street: 'Bahnhofstraße',
    number: '15',
    city: 'Munich',
    postalCode: '80335',
    country: 'Germany',
  },
  parents: [
    {
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      gender: 'Male',
      email: '<EMAIL>',
      mobile: '+49 123 456789',
      sportsAndInterests: 'Football, hiking, cooking',
      job: {
        description: 'Software Engineer',
        location: 'Munich',
        type: 'Full-time',
        workingDays: 'Monday to Friday',
        workFromHome: true,
        travel: false,
        changingEmployment: false,
      },
    },
    {
      firstName: 'Anna',
      lastName: 'Müller',
      gender: 'Female',
      email: '<EMAIL>',
      mobile: '+49 123 456790',
      sportsAndInterests: 'Yoga, reading, gardening',
      job: {
        description: 'Doctor',
        location: 'Munich',
        type: 'Part-time',
        workingDays: 'Monday, Wednesday, Friday',
        workFromHome: false,
        travel: false,
        changingEmployment: false,
      },
    },
  ],
  children: [
    {
      name: 'Jarred',
      age: 7,
      gender: 'Male',
    },
    {
      name: 'Lena',
      age: 5,
      gender: 'Female',
    },
  ],
  expectingNewborn: false,
  dueDate: null,
  family: {
    hasPets: true,
    petCare: true,
    specialDiet: false,
    dietType: null,
    dietDetails: null,
    acceptsVegetarian: true,
    smoking: false,
    acceptsSmoker: false,
    religious: false,
    religionDetails: null,
    practicesReligion: false,
    acceptsDifferentReligion: true,
    hasSpecialNeeds: false,
    specialNeedsDetails: null,
  },
  house: {
    type: 'Detached house',
    bedrooms: 4,
    bathrooms: 2,
    description: 'Spacious house with a large garden in a quiet neighborhood close to parks and schools.',
    aupairRoomSize: 20,
  },
  aupairRequirements: {
    searchDuration: '3-6 months',
    reasons: 'Childcare support and cultural exchange',
    stayDuration: '12 months',
    startDate: '2023-09-01',
    earliestStartDate: '2023-08-15',
    otherAgencies: false,
    previousAupair: true,
    preferredOrigin: 'EU',
    preferredCultures: 'European, South American',
    importantCharacteristics: 'Patient, responsible, energetic',
    importantSkills: 'First aid knowledge, cooking, driving',
    cooking: true,
    bikeRequired: false,
    drivingRequired: true,
    culturalExchangePlans: 'Family trips, language exchange, cooking traditional meals together',
    additionalLanguages: 'German (basic)',
  },
};

// Mock progress data - this would come from API in real implementation
const mockProgressData = {
  totalSteps: 20,
  completedSteps: 8,
  currentStage: 'Profile Setup',
  progress: 40, // percentage
};

export function FamilyDetails({ id }: { id: string }) {
  // add API information here
  const { totalSteps, completedSteps, currentStage, progress } = mockProgressData;

  return (
    <div className="space-y-4">
      {/* Family Journey Progress Bar */}
      <Card className="border-l-4 border-l-primary">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">Family Journey Progress</CardTitle>
              <CardDescription>Current stage: {currentStage}</CardDescription>
            </div>
            <Link to={`/dashboard/families/journey/${id}`}>
              <Button variant="outline" size="sm">
                <FileText className="mr-2 h-4 w-4" />
                View Details
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="font-medium">Overall Progress</span>
              <span className="text-muted-foreground font-semibold">{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{completedSteps} of {totalSteps} steps completed</span>
              <span>Family Journey Progress</span>
            </div>
          </div>
        </CardContent>
      </Card>
      <Tabs defaultValue="main" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="main">Main Contact</TabsTrigger>
          <TabsTrigger value="parents">Parents</TabsTrigger>
          <TabsTrigger value="children">Children</TabsTrigger>
          <TabsTrigger value="house">House</TabsTrigger>
          <TabsTrigger value="requirements">Requirements</TabsTrigger>
        </TabsList>
        <TabsContent value="main" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Main Contact Information</CardTitle>
              <CardDescription>Primary contact person for the family.</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Full Name</p>
                  <p className="text-sm text-muted-foreground">
                    {familyData.mainContact.firstName} {familyData.mainContact.lastName}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Email</p>
                  <p className="text-sm text-muted-foreground flex items-center">
                    <Mail className="mr-2 h-4 w-4" />
                    {familyData.mainContact.email}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Phone</p>
                  <p className="text-sm text-muted-foreground flex items-center">
                    <Phone className="mr-2 h-4 w-4" />
                    {familyData.mainContact.phone}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Address</CardTitle>
              <CardDescription>Family residence information.</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Street Address</p>
                  <p className="text-sm text-muted-foreground">
                    {familyData.address.street} {familyData.address.number}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">City</p>
                  <p className="text-sm text-muted-foreground">{familyData.address.city}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Postal Code</p>
                  <p className="text-sm text-muted-foreground">{familyData.address.postalCode}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Country</p>
                  <p className="text-sm text-muted-foreground flex items-center">
                    <MapPin className="mr-2 h-4 w-4" />
                    {familyData.address.country}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="parents" className="space-y-4">
          {familyData.parents.map((parent, index) => (
            <Card key={index}>
              <CardHeader>
                <CardTitle>
                  Parent {index + 1}: {parent.firstName} {parent.lastName}
                </CardTitle>
                <CardDescription>Information about the parent.</CardDescription>
              </CardHeader>
              <CardContent className="grid gap-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Gender</p>
                    <p className="text-sm text-muted-foreground">{parent.gender}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Email</p>
                    <p className="text-sm text-muted-foreground flex items-center">
                      <Mail className="mr-2 h-4 w-4" />
                      {parent.email}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Mobile</p>
                    <p className="text-sm text-muted-foreground flex items-center">
                      <Phone className="mr-2 h-4 w-4" />
                      {parent.mobile}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Sports & Interests</p>
                    <p className="text-sm text-muted-foreground flex items-center">
                      <Heart className="mr-2 h-4 w-4" />
                      {parent.sportsAndInterests}
                    </p>
                  </div>
                </div>
                <div className="pt-2">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Job Description</p>
                      <p className="text-sm text-muted-foreground flex items-center">
                        <Briefcase className="mr-2 h-4 w-4" />
                        {parent.job.description}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Location</p>
                      <p className="text-sm text-muted-foreground">{parent.job.location}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Type</p>
                      <p className="text-sm text-muted-foreground">{parent.job.type}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Working Days</p>
                      <p className="text-sm text-muted-foreground flex items-center">
                        <Calendar className="mr-2 h-4 w-4" />
                        {parent.job.workingDays}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Works From Home</p>
                      <p className="text-sm text-muted-foreground">{parent.job.workFromHome ? 'Yes' : 'No'}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Travels for Work</p>
                      <p className="text-sm text-muted-foreground">{parent.job.travel ? 'Yes' : 'No'}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Changing Employment</p>
                      <p className="text-sm text-muted-foreground">{parent.job.changingEmployment ? 'Yes' : 'No'}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
        <TabsContent value="children" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Children</CardTitle>
              <CardDescription>Information about the children in the family.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {familyData.children.map((child, index) => (
                  <div key={index} className="p-4 border rounded-md">
                    <h4 className="font-medium mb-2">
                      Child {index + 1}: {child.name}
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <p className="text-sm font-medium">Age</p>
                        <p className="text-sm text-muted-foreground">{child.age} years</p>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">Gender</p>
                        <p className="text-sm text-muted-foreground">{child.gender}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <h4 className="font-medium mb-2">Additional Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Expecting a Newborn</p>
                    <p className="text-sm text-muted-foreground">{familyData.expectingNewborn ? 'Yes' : 'No'}</p>
                  </div>
                  {familyData.expectingNewborn && familyData.dueDate && (
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Due Date</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(familyData.dueDate).toLocaleDateString()}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Family Information</CardTitle>
              <CardDescription>Additional details about the family environment.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Has Pets</p>
                  <p className="text-sm text-muted-foreground flex items-center">
                    <PawPrint className="mr-2 h-4 w-4" />
                    {familyData.family.hasPets ? 'Yes' : 'No'}
                  </p>
                </div>
                {familyData.family.hasPets && (
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Au Pair to Care for Pets</p>
                    <p className="text-sm text-muted-foreground">{familyData.family.petCare ? 'Yes' : 'No'}</p>
                  </div>
                )}
                <div className="space-y-1">
                  <p className="text-sm font-medium">Special Diet</p>
                  <p className="text-sm text-muted-foreground flex items-center">
                    <Utensils className="mr-2 h-4 w-4" />
                    {familyData.family.specialDiet ? 'Yes' : 'No'}
                  </p>
                </div>
                {familyData.family.specialDiet && (
                  <>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Diet Type</p>
                      <p className="text-sm text-muted-foreground">{familyData.family.dietType}</p>
                    </div>
                    <div className="space-y-1 col-span-2">
                      <p className="text-sm font-medium">Diet Details</p>
                      <p className="text-sm text-muted-foreground">{familyData.family.dietDetails}</p>
                    </div>
                  </>
                )}
                <div className="space-y-1">
                  <p className="text-sm font-medium">Accepts Vegetarian/Vegan</p>
                  <p className="text-sm text-muted-foreground">{familyData.family.acceptsVegetarian ? 'Yes' : 'No'}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Smoking</p>
                  <p className="text-sm text-muted-foreground">{familyData.family.smoking ? 'Yes' : 'No'}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Accepts Smoker</p>
                  <p className="text-sm text-muted-foreground">{familyData.family.acceptsSmoker ? 'Yes' : 'No'}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Religious</p>
                  <p className="text-sm text-muted-foreground">{familyData.family.religious ? 'Yes' : 'No'}</p>
                </div>
                {familyData.family.religious && (
                  <>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Religion Details</p>
                      <p className="text-sm text-muted-foreground">{familyData.family.religionDetails}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Practices Religion</p>
                      <p className="text-sm text-muted-foreground">
                        {familyData.family.practicesReligion ? 'Yes' : 'No'}
                      </p>
                    </div>
                  </>
                )}
                <div className="space-y-1">
                  <p className="text-sm font-medium">Accepts Different Religion</p>
                  <p className="text-sm text-muted-foreground">
                    {familyData.family.acceptsDifferentReligion ? 'Yes' : 'No'}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Special Needs</p>
                  <p className="text-sm text-muted-foreground">{familyData.family.hasSpecialNeeds ? 'Yes' : 'No'}</p>
                </div>
                {familyData.family.hasSpecialNeeds && (
                  <div className="space-y-1 col-span-2">
                    <p className="text-sm font-medium">Special Needs Details</p>
                    <p className="text-sm text-muted-foreground">{familyData.family.specialNeedsDetails}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="house" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>House & Surroundings</CardTitle>
              <CardDescription>Information about the family's home.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium">House Type</p>
                  <p className="text-sm text-muted-foreground flex items-center">
                    <Home className="mr-2 h-4 w-4" />
                    {familyData.house.type}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Number of Bedrooms</p>
                  <p className="text-sm text-muted-foreground">{familyData.house.bedrooms}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Number of Bathrooms</p>
                  <p className="text-sm text-muted-foreground">{familyData.house.bathrooms}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Au Pair Room Size</p>
                  <p className="text-sm text-muted-foreground">{familyData.house.aupairRoomSize} m²</p>
                </div>
                <div className="space-y-1 col-span-2">
                  <p className="text-sm font-medium">House & Garden Description</p>
                  <p className="text-sm text-muted-foreground">{familyData.house.description}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="requirements" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Au Pair Requirements</CardTitle>
              <CardDescription>What the family is looking for in an au pair.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Search Duration</p>
                  <p className="text-sm text-muted-foreground">{familyData.aupairRequirements.searchDuration}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Reasons for Au Pair</p>
                  <p className="text-sm text-muted-foreground">{familyData.aupairRequirements.reasons}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Stay Duration</p>
                  <p className="text-sm text-muted-foreground">{familyData.aupairRequirements.stayDuration}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Preferred Start Date</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(familyData.aupairRequirements.startDate).toLocaleDateString()}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Earliest Start Date</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(familyData.aupairRequirements.earliestStartDate).toLocaleDateString()}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Listed with Other Agencies</p>
                  <p className="text-sm text-muted-foreground">
                    {familyData.aupairRequirements.otherAgencies ? 'Yes' : 'No'}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Had Previous Au Pair</p>
                  <p className="text-sm text-muted-foreground">
                    {familyData.aupairRequirements.previousAupair ? 'Yes' : 'No'}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Preferred Origin</p>
                  <p className="text-sm text-muted-foreground">{familyData.aupairRequirements.preferredOrigin}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Preferred Cultures</p>
                  <p className="text-sm text-muted-foreground">{familyData.aupairRequirements.preferredCultures}</p>
                </div>
                <div className="space-y-1 col-span-2">
                  <p className="text-sm font-medium">Important Characteristics</p>
                  <p className="text-sm text-muted-foreground">
                    {familyData.aupairRequirements.importantCharacteristics}
                  </p>
                </div>
                <div className="space-y-1 col-span-2">
                  <p className="text-sm font-medium">Important Skills</p>
                  <p className="text-sm text-muted-foreground">{familyData.aupairRequirements.importantSkills}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Cooking Required</p>
                  <p className="text-sm text-muted-foreground">
                    {familyData.aupairRequirements.cooking ? 'Yes' : 'No'}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Bike Required</p>
                  <p className="text-sm text-muted-foreground">
                    {familyData.aupairRequirements.bikeRequired ? 'Yes' : 'No'}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Driving Required</p>
                  <p className="text-sm text-muted-foreground">
                    {familyData.aupairRequirements.drivingRequired ? 'Yes' : 'No'}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">Additional Languages</p>
                  <p className="text-sm text-muted-foreground">{familyData.aupairRequirements.additionalLanguages}</p>
                </div>
                <div className="space-y-1 col-span-2">
                  <p className="text-sm font-medium">Cultural Exchange Plans</p>
                  <p className="text-sm text-muted-foreground">{familyData.aupairRequirements.culturalExchangePlans}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
