import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Download, ExternalLink } from "lucide-react"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

export default function IntakeStep() {
  return (
    <div className="space-y-8">
      <Card className="border-l-8 border-l-black shadow-lg">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center space-x-3">
            <span className="bg-black text-white px-4 py-2 rounded-full text-sm font-bold">Step 1</span>
            <span className="text-2xl font-bold">Intake & Confirmation</span>
          </CardTitle>
          <CardDescription className="text-lg text-gray-600">
            Welcome to your HBN journey! Before the intake, please confirm the Assignment and review the Host
            Guidelines.
          </CardDescription>
        </CardHeader>
      </Card>

      <Accordion type="single" collapsible defaultValue="item-1" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-gray-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <CheckCircle className="h-6 w-6 text-black" />
              <span className="text-lg font-semibold">1.1 Assignment Confirmation</span>
              <Badge className="bg-black text-white">Completed</Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="grid md:grid-cols-3 gap-8">
                  <div className="md:col-span-2 space-y-6">
                    <p className="text-gray-700 text-lg leading-relaxed">
                      Please read the Assignment Confirmation including Data Processing Agreement (AVG), our general
                      terms and conditions, the IND brochure and HBN fees carefully, in order for you to confirm the
                      assignment confirmation here.
                    </p>

                    <div className="space-y-4">
                      <h4 className="font-bold text-black text-xl">To Do:</h4>
                      <ul className="space-y-3 text-gray-700">
                        <li className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                          <ExternalLink className="h-5 w-5 text-black" />
                          <span className="flex-1">IND Brochure Cultural Exchange NL</span>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-black text-black hover:bg-black hover:text-white"
                          >
                            Download
                          </Button>
                        </li>
                        <li className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                          <ExternalLink className="h-5 w-5 text-black" />
                          <span className="flex-1">IND Brochure Cultural Exchange ENG</span>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-black text-black hover:bg-black hover:text-white"
                          >
                            Download
                          </Button>
                        </li>
                        <li className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                          <ExternalLink className="h-5 w-5 text-black" />
                          <span className="flex-1">HBN Terms & Conditions</span>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-black text-black hover:bg-black hover:text-white"
                          >
                            View
                          </Button>
                        </li>
                      </ul>
                    </div>

                    <div className="flex items-center space-x-3 p-6 bg-gray-50 rounded-lg border-2 border-gray-200">
                      <Checkbox id="confirm-assignment" checked disabled />
                      <label htmlFor="confirm-assignment" className="text-lg font-semibold text-black">
                        Confirm Assignment
                      </label>
                    </div>
                    <p className="text-sm text-gray-500">
                      By clicking on this button, you agree to the assignment and our Terms & Conditions
                    </p>
                  </div>

                  <div className="bg-black text-white p-8 rounded-lg">
                    <h3 className="text-xl font-bold mb-6">Assignment Confirmation</h3>
                    <p className="text-sm mb-6 text-gray-300">
                      <strong>NOTE:</strong> Pop-up may be blocked, please allow pop-ups for this website.
                    </p>
                    <Button className="w-full bg-white text-black hover:bg-gray-100">
                      <Download className="h-4 w-4 mr-2" />
                      DOWNLOAD
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="item-2" className="border-2 border-gray-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <CheckCircle className="h-6 w-6 text-black" />
              <span className="text-lg font-semibold">1.2 Intake Meeting & Guidelines</span>
              <Badge className="bg-black text-white">Completed</Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="grid md:grid-cols-3 gap-8">
                  <div className="md:col-span-2 space-y-6">
                    <div>
                      <h4 className="font-bold text-black text-xl mb-3">VIDEO INTAKE MEETING</h4>
                      <p className="text-gray-700 text-lg leading-relaxed">
                        To understand your family situation and provide detailed information about the process and legal
                        requirements, we will schedule a video call during office hours.
                      </p>
                    </div>

                    <div>
                      <h4 className="font-bold text-black text-xl mb-3">HOST-FAMILY GUIDELINES</h4>
                      <p className="text-gray-700 text-lg leading-relaxed">
                        The Host Family Guidelines include legal and practical information, as well as tips on
                        communication, personal interaction, and handling cultural differences.
                      </p>
                    </div>

                    <div className="flex items-center space-x-3 p-6 bg-gray-50 rounded-lg border-2 border-gray-200">
                      <Checkbox id="confirm-guidelines" checked disabled />
                      <label htmlFor="confirm-guidelines" className="text-lg font-semibold text-black">
                        Confirm Guidelines
                      </label>
                    </div>
                  </div>

                  <div className="bg-gray-900 text-white p-8 rounded-lg">
                    <h3 className="text-xl font-bold mb-6">Host Family Guidelines</h3>
                    <p className="text-sm mb-6 text-gray-300">Download and read this document</p>
                    <Button className="w-full bg-white text-black hover:bg-gray-100">
                      <Download className="h-4 w-4 mr-2" />
                      DOWNLOAD
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <div className="flex justify-end pt-8">
        <Button size="lg" className="bg-black text-white hover:bg-gray-800 px-8 py-3 text-lg">
          GO TO STEP 2
        </Button>
      </div>
    </div>
  )
}
