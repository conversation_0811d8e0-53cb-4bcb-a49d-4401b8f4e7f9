import {
  User,
  FileCheck,
  Video,
  Users,
  Heart,
  FileText,
  CalendarPlus2Icon as CalendarIcon2,
  Shield,
  DollarSign,
  MapPin,
  Home,
  CheckCircle,
  GraduationCap,
} from 'lucide-react';
import stagesData from './familyJourneyStages.json';

// Icon mapping from string names to actual icon components
const iconMap = {
  User,
  FileCheck,
  Video,
  Users,
  Heart,
  FileText,
  CalendarIcon2,
  Shield,
  DollarSign,
  MapPin,
  Home,
  CheckCircle,
  GraduationCap,
};

// Type definitions
export interface Step {
  id: string;
  title: string;
  description: string;
  type: string;
  icon: React.ElementType;
}

export interface Stage {
  id: number;
  title: string;
  description: string;
  icon: React.ElementType;
  steps: Step[];
}

// Transform the JSON data to include actual icon components
export const stages: Stage[] = stagesData.map((stage) => ({
  ...stage,
  icon: iconMap[stage.icon as keyof typeof iconMap] || FileText,
  steps: stage.steps.map((step) => ({
    ...step,
    icon: iconMap[step.icon as keyof typeof iconMap] || FileText,
  })),
}));

export default stages;
